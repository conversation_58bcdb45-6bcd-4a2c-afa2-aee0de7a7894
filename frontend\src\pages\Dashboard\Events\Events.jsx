import { CircularProgress, Grid } from "@mui/material";
import React, { useEffect, useState, memo, useCallback, useRef } from "react";
import axiosInstance from "../../../axios";
import DetailModal from "./DetailModal";
import FilterEventModal from "./FilterEventModal";
import { useParams } from "react-router-dom";
import theme from "../../../theme";
import { getSocket } from "../../../socket";
import VirtualizedCardList from "./VirtualizedCardList";

const Events = ({ showFilterModal, setShowFilterModal, searchFilters, vessels, tab }) => {
    const { id } = useParams();
    const [events, setEvents] = useState([]);
    const [filteredEvents, setFilteredEvents] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [hasMore, setHasMore] = useState(true);
    const [page, setPage] = useState(1);
    const [filterItem, setFilterItem] = useState({});
    const [filters, setFilters] = useState({});
    const [favouriteArtifacts, setFavouriteArtifacts] = useState([]);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState(null);
    const virtualizedListContainerRef = useRef();
    const loadMoreTimeoutRef = useRef();
    const listRef = useRef();

    // TODO: review code for performance optimizations

    const fetchFilters = async () => {
        try {
            const filterItems = await axiosInstance
                .get("/artifacts/filters")
                .then((res) => res.data)
                .catch((err) => {
                    console.error(`Error fetching filters in Events`, err);
                });
            if (Object.keys(filterItems).length !== 0) {
                setFilterItem(filterItems);
            }
        } catch (err) {
            console.error(`Error fetching filters in Events`, err);
        }
    };

    const fetchArtifacts = async (isSocketTriggered = false, isLoadMore = false) => {
        if (!isSocketTriggered) {
            setIsLoading(true);
        }

        try {
            const currentPage = isLoadMore ? page + 1 : 1;
            const payload = {
                page: currentPage,
                pageSize: 100,
                filters: Object.keys(searchFilters).length ? searchFilters : filters,
                favourites: 1,
            };

            // If id is present, fetch single artifact
            if (id) {
                payload.filters = { ...payload.filters, id };
                payload.pageSize = 1;
            }

            const response = await axiosInstance.post("/artifacts", payload);
            const { artifacts, favouritesArtifacts, totalCount } = response.data;

            if (isLoadMore) {
                setEvents((prev) => [...prev, ...artifacts]);
                setPage(currentPage);
            } else {
                setEvents(artifacts);
                setPage(1);
            }

            setFavouriteArtifacts(favouritesArtifacts);
            setHasMore(artifacts.length < (id ? 1 : totalCount)); // Adjust hasMore based on single/list view
            setIsLoading(false);
            if (id && artifacts.length > 0) {
                const artifact = artifacts[0];
                const vesselInfo = vessels.find((v) => v.unit_id === artifact.unit_id);
                setSelectedCard({
                    ...artifact,
                    vesselName: vesselInfo?.name || vesselInfo?.unit_id,
                });
                setShowDetailModal(true);
            }
        } catch (err) {
            console.error(`Error fetching artifacts in Events`, err);
            setIsLoading(false);
        }
    };

    const handleLoadMore = useCallback(() => {
        if (!isLoading && hasMore && !id) {
            // Clear any existing timeout
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }

            // Set a new timeout
            loadMoreTimeoutRef.current = setTimeout(() => {
                fetchArtifacts(false, true);
            }, 500);
        }
    }, [isLoading, hasMore, id, fetchArtifacts]);

    useEffect(() => {
        fetchFilters();
        if (sessionStorage.getItem("eventPath")) {
            sessionStorage.removeItem("eventPath");
        }
    }, []);

    useEffect(() => {
        // Filter events to only include those with vessels that have valid unit_ids
        const validVesselUnitIds = vessels.map(v => v.unit_id).filter(Boolean);
        const filteredEventsData = events.filter(event =>
            event.unit_id && validVesselUnitIds.includes(event.unit_id)
        );
        setFilteredEvents(filteredEventsData);
    }, [events, vessels]);

    useEffect(() => {
        fetchArtifacts();
    }, [filters, searchFilters, id]);

    useEffect(() => {
        if (tab !== "events") {
            setEvents((prev) => prev.slice(0, 100));
            setPage(1);
            setIsLoading(false);
            if (listRef.current) {
                listRef.current.scrollToTop();
            }
        }
    }, [tab]);

    const handleFavouriteChange = useCallback((artifact) => {
        setFavouriteArtifacts((prev) => {
            const isFavourite = prev.some((fav) => fav.artifact_id === artifact.artifact_id);
            if (isFavourite) {
                return prev.filter((fav) => fav.artifact_id !== artifact.artifact_id);
            }
            return [...prev, { _id: artifact._id, artifact_id: artifact.artifact_id }];
        });
    }, []);

    useEffect(() => {
        const socket = getSocket();

        socket.on("favourites/changed", handleFavouriteChange);

        return () => {
            socket.off("favourites/changed", handleFavouriteChange);
            // Clear timeout on unmount
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }
        };
    }, []);

    const autoRefreshTimerRef = useRef();
    useEffect(() => {
        if (id) return;
        if (autoRefreshTimerRef.current) clearInterval(autoRefreshTimerRef.current);

        autoRefreshTimerRef.current = setInterval(
            () => {
                console.info("Auto-refreshing events (5-minute interval)");
                fetchArtifacts(true);
            },
            5 * 60 * 1000,
        );

        return () => {
            if (autoRefreshTimerRef.current) {
                clearInterval(autoRefreshTimerRef.current);
            }
        };
    }, [filters, searchFilters, fetchArtifacts, id]);

    return (
        <>
            {isLoading && events.length === 0 ? (
                <Grid
                    container
                    display={"flex"}
                    justifyContent={"center"}
                    alignItems={"center"}
                    height={{ xs: "90%", sm: "90%" }}
                    overflow={"auto"}
                    marginBottom={2}
                    size="grow"
                >
                    <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={60} />
                </Grid>
            ) : (
                <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
                    <Grid
                        container
                        overflow={"auto"}
                        display={"block"}
                        border={`1px solid ${theme.palette.custom.borderColor}`}
                        borderRadius={"10px"}
                        padding={"10px 24px"}
                        size="grow"
                    >
                        <Grid container height={"100%"} overflow={"hidden"} ref={virtualizedListContainerRef}>
                            <VirtualizedCardList
                                ref={listRef}
                                events={filteredEvents}
                                setShowDetailModal={setShowDetailModal}
                                setSelectedCard={setSelectedCard}
                                favouriteArtifacts={favouriteArtifacts}
                                vessels={vessels}
                                isLoading={isLoading}
                                onLoadMore={handleLoadMore}
                                hasMore={hasMore && !id} // Disable load more in single view
                                containerRef={virtualizedListContainerRef}
                            />
                        </Grid>
                    </Grid>
                    <DetailModal
                        showDetailModal={showDetailModal}
                        setShowDetailModal={setShowDetailModal}
                        selectedCard={selectedCard}
                        setSelectedCard={setSelectedCard}
                        favouriteArtifacts={favouriteArtifacts}
                        id={id}
                    />
                    <FilterEventModal
                        showFilterModal={showFilterModal}
                        vessels={vessels}
                        setFilters={setFilters}
                        setShowFilterModal={setShowFilterModal}
                        events={events}
                        setFilteredEvents={setFilteredEvents}
                        filterItems={filterItem}
                    />
                </Grid>
            )}
        </>
    );
};

export default memo(Events);
