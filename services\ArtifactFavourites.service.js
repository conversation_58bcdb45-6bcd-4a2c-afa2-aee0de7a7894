const ArtifactFavourite = require("../models/ArtifactFavourites");
const db = require("../modules/db");
const { validateError } = require("../utils/functions");
const { populateLinkedUnitsInArray } = require("../utils/linkedUnits");
const { permissions } = require("../utils/permissions");
const streamService = require("./Stream.service");

async function addFavouriteArtifact(req, res) {
    const { user_id, artifact_id } = req.body;

    if (!user_id || !artifact_id) {
        return res.status(400).send({ message: "user_id and artifact_id are required" });
    }

    try {
        const isAlreadyExists = await ArtifactFavourite.findOne({ user_id, artifact_id });
        if (isAlreadyExists) {
            return res.status(400).send({ message: "Artifact already in favourites" });
        }
        const newFavourite = new ArtifactFavourite({ user_id, artifact_id });
        await newFavourite.save();
        res.status(201).send({ message: "Artifact added to favourites" });
    } catch (error) {
        validateError(error, res);
    }
}

async function getAllFavouriteArtifacts(req, res) {
    try {
        const favourites = await ArtifactFavourite.find({});
        res.status(200).send(favourites);
    } catch (error) {
        validateError(error, res);
    }
}

async function deleteFavouriteArtifact(req, res) {
    const { user_id, artifact_id } = req.body;

    if (!user_id || !artifact_id) {
        return res.status(400).send({ message: "user_id and artifact_id are required" });
    }

    try {
        const result = await ArtifactFavourite.findOneAndDelete({ user_id, artifact_id });
        if (!result) {
            return res.status(404).send({ message: "Artifact not found in favourites" });
        }
        res.status(200).send({ message: "Artifact removed from favourites" });
    } catch (error) {
        validateError(error, res);
    }
}

async function getUserFavouriteArtifacts(req, res) {
    const { user_id } = req.params;

    if (!user_id) {
        return res.status(400).send({ message: "user_id is required" });
    }

    try {
        const allFavorites = await ArtifactFavourite.find({ user_id }).select("artifact_id");
        const artifactIds = allFavorites.map((fav) => fav.artifact_id);
        let allowedUnits = [];
        if (req.user) {
            if (!req.user.permissions.find((p) => p.permission_id === permissions.accessAllUnits)) {
                allowedUnits = populateLinkedUnitsInArray(req.user.allowed_units.map((v) => v.unit_id));
            } else {
                const allUnits = await streamService.fetchAll({});
                allowedUnits = populateLinkedUnitsInArray(allUnits.map((v) => v.unit_id));
            }
        }

        const artifacts = await db.qmai
            .collection("analysis_results")
            .find(
                {
                    _id: { $in: artifactIds },
                    unit_id: { $in: allowedUnits },
                },
                {
                    projection: {
                        _id: 1,
                        unit_id: 1,
                        bucket_name: 1,
                        image_path: 1,
                        video_path: 1,
                        location: 1,
                        category: 1,
                        super_category: 1,
                        size: 1,
                        color: 1,
                        weapons: 1,
                        others: 1,
                        timestamp: 1,
                        onboard_vessel_name: 1,
                        country_flag: 1,
                        aws_region: 1,
                        text_extraction: 1,
                        imo_number: 1,
                    },
                },
            )
            .toArray();
        const favourites = allFavorites.filter((fav) => artifacts.some((art) => art._id.toString() === fav.artifact_id.toString()));
        res.status(200).send({ favourites, artifacts });
    } catch (error) {
        validateError(error, res);
    }
}

module.exports = {
    addFavouriteArtifact,
    getAllFavouriteArtifacts,
    deleteFavouriteArtifact,
    getUserFavouriteArtifacts,
};
